use color_eyre::eyre::OptionExt;
use common::utils::stats::calculate_sum;
use dw_test_item::task::cp_task_params::CpTaskParams;
use dw_test_item::task::cp_test_item_task::CpTestItemTask;
use jni::objects::JClass;
use jni::sys::{jdoubleArray, jobject, jstring};
use jni::JNIEnv;
use jni_bridge::jni_bridge::JavaClasses;
use jni_bridge::{create_big_decimal, get_double_array, handle_unwinded_scope};
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;

use color_eyre::Result;
use std::error::Error;

#[no_mangle]
pub extern "system" fn Java_com_guwave_onedata_dataware_dw_testItem_spark_jni_Native_00024_sum(
    mut env: JNIEnv,
    _class: JClass,
    values: jdoubleArray,
) -> jobject {
    if !JavaClasses::inited() {
        JavaClasses::init(&env);
    }
    handle_unwinded_scope(|| -> Result<jobject, Box<dyn Error + Send + Sync>> {
        let arr = get_double_array(&mut env, values).ok_or_eyre("Failed to get double array")?;
        let value = calculate_sum(&arr).ok_or_eyre("Failed to calculate sum")?;
        Ok(create_big_decimal(&mut env, value))
    })
}

#[no_mangle]
pub extern "system" fn Java_com_guwave_onedata_dataware_dw_testItem_spark_jni_Native_00024_cpTask(
    env: JNIEnv,
    _class: JClass,
    value: jstring,
) -> bool {
    if !JavaClasses::inited() {
        JavaClasses::init(&env);
    }
    unsafe {
        std::env::set_var("RUST_BACKTRACE", "1");
    }
    let param = value.into();
    handle_unwinded_scope(|| -> Result<bool, Box<dyn Error + Send + Sync>> {
        color_eyre::install()?;
        tracing_subscriber::fmt()
            .with_max_level(Level::INFO)
            .with_span_events(FmtSpan::FULL)
            .with_file(true)
            .with_line_number(true)
            .init();

        // Create a new Tokio runtime for executing async code
        let rt = tokio::runtime::Runtime::new()?;

        let task = CpTestItemTask::new();
        rt.block_on(async {
            task.do_task(CpTaskParams::new(param))
            .await
        })?;

        Ok(true)
    })
}
